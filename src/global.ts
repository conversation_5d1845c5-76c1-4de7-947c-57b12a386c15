declare global {
  interface Window {
    BlmUtils: any;
    blmcpUi: any;
    blmBusinessComponents: any;
    mobileLib: any;
    AliLowCodeEngine: any;
    $legoAssets: any;
  }
}

// 入口
import * as legoRequest from '@blmcp/peento-request';
// @ts-expect-error
import * as BlmUtils from 'blm-utils';
import dayjs from 'dayjs';
import localeData from '@/utils/dayjs_locale';

localeData(undefined, dayjs, dayjs);

if (process.env.NODE_ENV === 'development') {
  window.BlmUtils = BlmUtils;
}
// 挂载全局变量
window.legoRequest = legoRequest;
