import React, { forwardRef, useEffect, useRef, useState } from 'react';
import { Collapse, message, Spin, Tooltip } from '@blmcp/ui';
import './index.less';
import { Coordinate } from '@blmcp/charts';
import { LoadingOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { reportStore } from '@blm/bi-lego-sdk/dist/es/utils';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';
import {
  blmAnalysisModuleClick,
  blmAnalysisModuleExposure,
} from '@/utils/eventTracking';

import linkageCenterExp from '@/pages/lego/libraryMaterials/module/Linkage/index';
import { getCityInfo } from '@/pages/lego/utils';
import {
  bosQueryRuleConfigByAdcode,
  queryBaseCntOrderDoneChartReport,
  queryBaseCntOrderDoneReport,
  queryMultiCityChartReport,
} from '../../api/aiAttribution';
import LoadingIcon from './img/loading_icon.svg';
import Point from './img/point.svg';
import NoData from './img/noData.svg';
import LayerConfig from './LayerConfig';
import ReportDetails from './reportDetails/index';
import CityDetailedData from './cityDetailedData/index';

const genExtra = (cClick, dClick, cityDetailClick, conditionType, btnType) => {
  const configClick = (event) => {
    blmAnalysisModuleClick({
      pageId: 'p_leopard_cp_00000480',
      eventId: 'e_leopard_cp_click_00003042',
    });
    event.stopPropagation();
    if (cClick) {
      cClick();
    }
  };
  const detailClick = (event) => {
    // 归因报告详情按钮 - 点击
    blmAnalysisModuleClick({
      pageId: 'p_leopard_cp_00000480',
      eventId: 'e_leopard_cp_click_00003038',
    });
    event.stopPropagation();
    if (dClick) {
      dClick();
    }
  };

  // 城市明细数据
  const cityDetailedClick = (event) => {
    // 城市明细数据 - 点击
    blmAnalysisModuleClick({
      pageId: 'p_leopard_cp_00000480',
      eventId: 'e_leopard_cp_click_00003120',
    });
    event.stopPropagation();
    if (cityDetailClick) {
      cityDetailClick();
    }
  };

  const detailTip = () => {
    let tipTitle = <></>;
    if (conditionType === 0) {
      tipTitle = (
        <Tooltip title="数据正在生成中...">
          <span className="tipTitleCss">归因报告详情</span>
        </Tooltip>
      );
    } else if (conditionType === 3) {
      tipTitle = (
        <Tooltip title="'暂无数据权限；请联系管理员添加'">
          <span className="tipTitleCss">归因报告详情</span>
        </Tooltip>
      );
    } else {
      // 1-出车司机数过低/2-数据正常
      tipTitle = <span onClick={detailClick}>归因报告详情</span>;
    }

    return tipTitle;
  };

  const cityDetailTip = () => {
    let tipTitle = <></>;
    if (conditionType === 0) {
      tipTitle = (
        <Tooltip title="数据正在生成中...">
          <span className="tipTitleCss">城市明细数据</span>
        </Tooltip>
      );
    } else if (conditionType === 3) {
      tipTitle = (
        <Tooltip title="'暂无数据权限；请联系管理员添加'">
          <span className="tipTitleCss">城市明细数据</span>
        </Tooltip>
      );
    } else {
      tipTitle = <span onClick={cityDetailedClick}>城市明细数据</span>;
    }

    return tipTitle;
  };

  return (
    <div className="button_detail">
      <span onClick={configClick}>分层条件配置</span>
      {btnType === 1 ? detailTip() : cityDetailTip()}
    </div>
  );
};

const Aicontent = (props, ref) => {
  const linkageCenter = linkageCenterExp(props.uuid);
  const queryCenter = queryCenterExp(props.uuid);
  const relationCenter = relationCenterExp(props.uuid);
  // 首屏内容显示状态： 修改前： 0-数据生成中，1-单一城市， 2-正常状态   修改后：0-数据生成中， 1-单一城市， 2-正常状态
  const [condition, setCondition] = useState(2);
  const [cityInfo, setCityInfo] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dataset, setDataset] = useState([]);
  const [report, setReport] = useState(''); // 报告
  const [relatedIndex, setRelatedIndex] = useState(''); // 相关指标
  const [dataInterpretation, setDataInterpretation] = useState(''); //数据解读
  // 城市权限： 单城市权限-power:"single"，多城市权限-power:"more"
  const cityPowerRef = useRef({});
  const [cityOptions, setCityOptions] = useState([]);
  const innerRef = useRef(null);
  const dimensions = [
    { name: 'date' },
    { name: 'cntOrdCall', displayName: '(求和)发单量', seriesType: 'bar' },
    {
      name: 'cntOrdDone',
      displayName: '(求和)完单量',
      seriesType: 'line',
      yAxisIndex: 1,
    },
  ];

  // 格式化日期为“X月X日”
  const formatDateToMonthDay = (date) => {
    const month = String(date.month() + 1).padStart(2, '0'); // 月份是从0开始的
    const day = String(date.date()).padStart(2, '0');
    return `${month}月${day}日`;
  };
  const getTime = (val) => {
    // 获取当前日期
    let currentDate = dayjs();
    let textName = '';
    if (val) {
      let previousDay = currentDate.subtract(val, 'day');
      textName = formatDateToMonthDay(previousDay);
    }
    return textName;
  };

  // 单个城市数据查询
  const querySingleCityData = async (cityId) => {
    // 数据状态：0-数据生成中，1-出车司机数过低, 2-正常，
    let extType = 2;
    try {
      setLoading(true);
      const adcodeSingle = cityId ?? '';
      setCityInfo([adcodeSingle]);
      let adcodeInfo = {};
      adcodeInfo = await bosQueryRuleConfigByAdcode({ adcode: adcodeSingle });
      if (adcodeInfo?.code === 1) {
        // 单城市权限时要 获取layerType
        let layerType = '3';
        // 单城市数据查询的时候code为1且data为null时，表示当前城市为无权限状态
        if (adcodeInfo.data === null) {
          extType = 3;
          return { extTypeSingle: extType };
        } else if (adcodeInfo?.data?.layerDimList?.length > 0) {
          layerType = adcodeInfo.data.layerDimList[0];
        }
        // 首屏+报告
        const res = await queryBaseCntOrderDoneChartReport({
          adcode: adcodeSingle,
          layerType,
        });
        if (res.code === 1) {
          if (
            res.data === null ||
            res.data === '' ||
            res.data?.report === null ||
            res.data?.report === ''
          ) {
            // 数据正在加载中
            extType = 0; // 数据生成中
            props?.setActiveKey([]); // 图表收起
          } else {
            props?.setActiveKey(['1']);
            setDataset(res.data.chart);
            setDataInterpretation(res.data.dataInterpretation);
            setRelatedIndex(res.data.relatedIndex);
            setReport(res.data.report);

            if (res.data.dataInterpretation === '-1') {
              // 出车司机数过低逻辑
              extType = 1;
              props?.setActiveKey([]);
            } else {
              extType = 2; // 数据正常
              props?.setActiveKey(['1']);
            }
          }
        } else {
          extType = 0;
          props?.setActiveKey([]);
        }
      } else {
        extType = 0;
        props?.setActiveKey([]);
        message.warning(adcodeInfo?.msg);
      }
      setLoading(false);
    } catch (err) {
      extType = 0;
      props?.setActiveKey([]);
      // message.warning(err);
    }

    return { extTypeSingle: extType };
  };

  const queryData = async () => {
    // 按钮类型显示状态： 1-归因报告详情，2-城市明细数据
    let btnType = 2;
    // 数据状态：0-数据生成中，1-出车司机数过低, 2-正常，3: 无权限状态
    let extType = 2;

    // 判断城市的权限
    let cityPowerInfo = {};
    cityPowerInfo = cityPowerRef?.current;
    if (!cityPowerInfo?.power || cityPowerInfo.power === '') {
      props?.setActiveKey([]);
      setCondition(0);
      // 将数据状态传给父组件
      if (props.getConditionType) {
        props.getConditionType(0, 2);
      }
      return;
    }

    // 单个城市权限
    if (cityPowerInfo?.power === 'single') {
      // 1. 单城市权限时，按钮展示 归因报告详情
      btnType = 1;
      if (extType !== 3) {
        const { extTypeSingle } = await querySingleCityData(
          cityPowerInfo?.cityCode,
        );
        extType = extTypeSingle;
      }
    }
    // 多城市权限
    else {
      // 获取选中的城市数据
      const filter = queryCenter.getQuery();
      let cityInfoFilter = _.find(filter.filterInfo, (o) => {
        return o.key === 'adcode';
      });
      if (cityInfoFilter) {
        const cityInfoArr = cityInfoFilter.fieldValue;
        setCityInfo(cityInfoArr);
        // 选择了单个城市 - 显示归因报告详情按钮
        if (cityInfoArr?.length === 1 && cityInfoArr[0]) {
          btnType = 1;
          if (extType !== 3) {
            const { extTypeSingle } = await querySingleCityData(cityInfoArr[0]);
            extType = extTypeSingle;
          }
        } else {
          btnType = 2;
          if (extType !== 3) {
            // 获取多城市权限下 首屏+报告 数据
            try {
              setLoading(true);
              // 多城市权限获取 首屏+报告 数据
              const res = await queryMultiCityChartReport({
                adcode: cityInfoArr,
              });
              if (res.code === 1 && res.data) {
                if (res.data?.report) {
                  props?.setActiveKey(['1']);
                  setDataset(res.data.chart);
                  setReport(res.data?.header ?? ''); // 顶部报告
                  setRelatedIndex(res.data?.title ?? ''); // 相关指标
                  setDataInterpretation(res.data.report);
                  extType = 2; // 数据正常
                } else if (res.data?.report === '-1') {
                  // 出车司机数过少
                  extType = 1;
                  props?.setActiveKey([]);
                  setDataset(res.data.chart);
                  setReport(res.data?.header ?? ''); // 顶部报告
                  setRelatedIndex(''); // 相关指标
                  setDataInterpretation('');
                } else {
                  // 数据正在加载中
                  extType = 0; // 数据状态
                  props?.setActiveKey([]); // 图表收起
                }
                // 判断二阶段接口改造后，是否展示无权限状态,false表示展示无权限状态
                if (!res.data?.hasPermission) {
                  extType = 3;
                  props?.setActiveKey([]); // 图表收起
                }
              } else {
                extType = 0;
                props?.setActiveKey([]);
              }
              setLoading(false);
            } catch (err) {
              extType = 0;
              props?.setActiveKey([]);
              // message.warning(err);
            }
          }
        }
      }
    }

    setCondition(extType);
    // 将数据状态传给父组件
    if (props.getConditionType) {
      props.getConditionType(extType, btnType);
    }

    // 智能归因曝光上报
    blmAnalysisModuleExposure({
      pageId: 'p_leopard_cp_00000480',
      eventId: 'e_leopard_cp_exposure_00003116',
      ext: {
        // Unscreened:未筛选城市,Screened:筛选单一城市
        str0_e: btnType === 1 ? 'Screened' : 'Unscreened',
        // Nodata:无数据,normal:正常,Lessdata:数据量少
        str1_e:
          extType === 1 ? 'Lessdata' : extType === 2 ? 'normal' : 'Nodata',
      },
    });
  };

  // 监听滑动事件上报埋点
  useEffect(() => {
    // 监听的模块 result
    let module = document.getElementById('result');
    if (!module) {
      return;
    }
    // 定义滑动事件的处理函数
    const handleScroll = () => {
      // 滑动事件上报埋点
      blmAnalysisModuleClick({
        pageId: 'p_leopard_cp_00000480',
        eventId: 'e_leopard_cp_click_00003036',
      });
      // 移除事件监听，确保只触发一次
      module?.removeEventListener('scroll', handleScroll);
    };
    // 为模块添加事件监听器
    module?.addEventListener('scroll', handleScroll);
    // 清理函数：移除事件监听器
    return () => {
      module?.removeEventListener('scroll', handleScroll);
    };
  }, [cityInfo]);

  useEffect(() => {
    // 获取城市下拉数据·判断当前登录人的城市权限 cityPower
    const cityOptionsChange = (cityOptions) => {
      let info = {
        power: '',
        cityCode: '',
        cityName: '',
      };
      if (cityOptions?.length) {
        let arr = [];
        cityOptions.forEach((item) => {
          if (item?.childDictList) {
            arr = arr.concat(item?.childDictList);
          }
        });
        if (arr.length === 1) {
          info = {
            power: 'single',
            cityCode: arr[0]?.dictValue ?? '',
            cityName: arr[0]?.dictDesc ?? '',
          };
        } else {
          // 多城市权限/没有城市权限
          info = { power: 'more', cityCode: '', cityName: '' };
        }
        setCityOptions(arr);
        cityPowerRef.current = info;
      } else {
        message.error('当前账号未配置城市权限，请联系管理员');
      }
      setTimeout(() => {
        queryData();
      }, 0);
    };
    linkageCenter.subscribe('cityOptionsChange', cityOptionsChange);
    relationCenter.subscribe('all', 'chartId1', queryData);

    return () => {
      linkageCenter.unsubscribe('cityOptionsChange', cityOptionsChange);
    };
  }, []);

  useEffect(() => {
    const reportId = props.reportId;
    const status = reportStore.get(props.uuid)?.publishStatus;
    // 给内部的span添加事件监听器 注意：这里不会直接应用于dataInterpretation，因为它是一个HTML字符串
    if (innerRef.current) {
      // 获取带有cityId的span元素
      const spans = innerRef.current.querySelectorAll('div[cityId]');
      spans.forEach((span) => {
        span.style.color = '#366CFE';
        span.style.cursor = 'pointer';
        span.style.display = 'inline-block';
        // 给带有cityId的span元素添加简监听事件
        span.addEventListener('click', (event) => {
          // 获取cityId
          const cityId = event.target.getAttribute('cityid');
          const textContent =
            event.target.textContent || event.currentTarget.textContent;
          if (cityId === '999999') {
            // 打开城市明细数据
            props?.setOpenCityDetail(true);
          } else {
            const { pCityId } = getCityInfo(cityOptions, cityId);
            // share-url-hooks-disable-next-line
            window.open(
              `/qbi/legoBI/view?reportId=${reportId}&publish=${
                status || 0
              }&cityId=${cityId}&pCityId=${pCityId}`,
            );
          }
          blmAnalysisModuleClick({
            pageId: 'p_leopard_cp_00000480',
            eventId: 'e_leopard_cp_click_00003220',
            ext: {
              str0_e: textContent || '',
              str1_e: '报告明细',
            },
          });
        });
      });
    }

    // 清理函数
    return () => {
      if (innerRef.current) {
        const spans = innerRef.current.querySelectorAll('span[cityId]');
        spans.forEach((span) => {
          // 注意：这里的回调函数需要是之前添加的同一个函数
          span.removeEventListener('click', () => {});
        });
      }
    };
  }, [dataInterpretation]);

  const renderCp = () => {
    let CP = <></>;
    switch (condition) {
      case 0:
        CP = (
          <div className="loading">
            <LoadingIcon></LoadingIcon>
            <p>数据正在生成中…</p>
          </div>
        );
        break;
      case 1:
        CP = CP = (
          <Spin
            indicator={<LoadingOutlined style={{ fontSize: 54 }} spin />}
            spinning={loading}
          >
            <div className="chart-box">
              <div className="chart">
                <div
                  className="chart-title data-explain"
                  dangerouslySetInnerHTML={{ __html: report }}
                ></div>
                <div className="chart-render">
                  <Coordinate
                    option={{
                      grid: {
                        top: 40,
                      },
                    }}
                    ref={ref}
                    dataSource={{ dimensions, source: dataset }}
                  ></Coordinate>
                </div>
              </div>
              <div id="result" className="result">
                <div className="noData">
                  <NoData></NoData>
                  <span>
                    {getTime(8)}或{getTime(1)}出车司机数过少，暂无归因结论
                  </span>
                </div>
              </div>
            </div>
          </Spin>
        );
        break;
      case 2:
        CP = (
          <Spin
            indicator={<LoadingOutlined style={{ fontSize: 54 }} spin />}
            spinning={loading}
          >
            <div className="chart-box">
              <div className="chart">
                <div
                  className="chart-title data-explain"
                  dangerouslySetInnerHTML={{ __html: report }}
                ></div>
                <div className="chart-render">
                  <Coordinate
                    option={{
                      grid: {
                        top: 40,
                      },
                    }}
                    ref={ref}
                    dataSource={{ dimensions, source: dataset }}
                  ></Coordinate>
                </div>
              </div>
              <div id="result" className="result">
                {/* 单城市权限/选择了单城市时，会存在司机数过低的状态*/}
                <div className="relate">
                  <div
                    className="relate-content data-explain"
                    dangerouslySetInnerHTML={{ __html: relatedIndex }}
                  ></div>
                </div>
                <div ref={innerRef} className="interpretation">
                  <div
                    className={
                      cityPowerRef?.current?.power === 'single' ||
                      cityInfo.length === 1
                        ? 'interpretation-content data-explain'
                        : 'interpretation-content data-explain-morePower'
                    }
                    dangerouslySetInnerHTML={{ __html: dataInterpretation }}
                  ></div>
                </div>
              </div>
            </div>
          </Spin>
        );
        break;
      case 3:
        CP = (
          <div className="loading">
            <LoadingIcon></LoadingIcon>
            <p>暂无数据权限；请联系管理员添加。</p>
          </div>
        );
        break;
    }

    return CP;
  };

  const chartClick = () => {
    // setCondition(1);
  };

  return (
    <div className="detail" onClick={chartClick}>
      {renderCp()}
    </div>
  );
};

const AicontentWrapper = forwardRef(Aicontent);

export const AiAttribution = (props) => {
  console.log('AiAttribution-propspropsprops', props);
  const linkageCenter = linkageCenterExp(props.uuid);
  const queryCenter = queryCenterExp(props.uuid);
  const relationCenter = relationCenterExp(props.uuid);
  const [open, setOpen] = useState(false);
  const [openDetail, setOpenDetail] = useState(false);
  const [openCityDetail, setOpenCityDetail] = useState(false);
  const [cityInfo, setCityInfo] = useState([]);
  const [baseInfo, setBaseInfo] = useState({});
  const [layerType, setLayerType] = useState(3);
  // 数据状态：0-数据生成中， 1-未筛选单一城市， 2正常， 变更后为：0-数据生成中，1-出车司机数过低，2-正常
  const [conditionType, setConditionType] = useState(2);
  // 按钮类型显示状态： 1-归因报告详情，2-城市明细数据
  const [btnType, setBtnType] = useState(2);
  const [activeKey, setActiveKey] = useState([]);
  // 城市权限： 单城市权限-power:"single"，多城市权限-power:"more"
  const cityoptionsRef = useRef({});
  const [cityOptions, setCityOptions] = useState([]);
  const aiRef = useRef();
  const cClick = () => {
    setOpen(true);
  };

  // 获取基础数据
  const getQueryBaseCntOrderDoneReport = (type) => {
    queryBaseCntOrderDoneReport({
      adcode: cityInfo[0] || '',
      layerType: type,
    }).then((res) => {
      if (res.code === 1) {
        const pathList = res?.data?.pathList || [];
        if (pathList && pathList.length) {
          setBaseInfo(res.data);
          setOpenDetail(true);
        } else {
          message.error('请等待数据生成');
        }
      }
    });
  };
  const dClick = () => {
    if (cityInfo && cityInfo?.length) {
      bosQueryRuleConfigByAdcode({ adcode: cityInfo[0] }).then((res) => {
        if (res.code === 1 && res.data) {
          let layerType = 3;
          if (res?.data?.layerDimList?.length > 0) {
            layerType = res.data.layerDimList[0];
            setLayerType(layerType);
          } else {
            setLayerType(3);
          }
          // 获取基础数据
          getQueryBaseCntOrderDoneReport(layerType);
        }
      });
    }
  };

  const cityDetailClick = () => {
    setOpenCityDetail(true);
  };

  const items: any[] = [
    {
      key: '1',
      label: (
        <div className="title pointPig">
          <span>智能归因</span>
          <Tooltip
            placement={'right'}
            title={() => {
              let text =
                '筛选多个城市时，会产出有所有数据权限的城市的昨日完单量数据波动明细。<br/>筛选单一城市后，会产出所选城市的详细归因结论。<br/>(未筛选城市视为选择所有有权限的城市)<br/>需有城市全部权限才可以正常展示智能归因的数据，若显示无权限请联系管理员开通对应城市的全部权限。';
              return (
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  dangerouslySetInnerHTML={{ __html: text }}
                  style={{ padding: '4px 8px' }}
                />
              );
            }}
            overlayInnerStyle={{ width: '410px', padding: '0' }}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <Point></Point>
          </Tooltip>
        </div>
      ),
      children: (
        <AicontentWrapper
          uuid={props.uuid}
          ref={aiRef}
          getConditionType={(extType = 0, btnType = 2) => {
            setConditionType(extType);
            setBtnType(btnType);
          }}
          setActiveKey={setActiveKey}
          setOpenCityDetail={setOpenCityDetail}
        ></AicontentWrapper>
      ),
      extra: genExtra(cClick, dClick, cityDetailClick, conditionType, btnType),
      headerClass: 'ai-header',
      forceRender: true,
    },
  ];

  // callback
  const onClose = () => {
    setOpen(false);
  };
  const onCloseDetail = () => {
    setOpenDetail(false);
  };
  const onCloseCityDetail = () => {
    setOpenCityDetail(false);
  };

  // 切换面板的回调
  const changeCollapse = (val) => {
    // 上报收起状态点击事件 (自助报告-报告页-智能归因收起) Unfold:展开； fold：收起
    blmAnalysisModuleClick({
      pageId: 'p_leopard_cp_00000480',
      eventId: 'e_leopard_cp_click_00003034',
      ext: {
        str0_e: val?.length ? 'Unfold' : 'fold',
      },
    });
  };

  const queryData = () => {
    const filter = queryCenter.getQuery();
    let cityInfoFilter = _.find(filter.filterInfo, (o) => {
      return o.key === 'adcode';
    });
    if (cityInfoFilter) {
      setCityInfo(cityInfoFilter.fieldValue);
    }
    // 单城市权限时默认选中对应的城市ID
    if (
      cityoptionsRef?.current &&
      cityoptionsRef?.current?.power === 'single'
    ) {
      const arr = [cityoptionsRef?.current?.cityCode] ?? [];
      setCityInfo(arr);
    }
  };
  useEffect(() => {
    relationCenter.subscribe('all', 'chartId', queryData);
    linkageCenter.notify('cityFilter');

    const cityOptionsChange = (cityOptions) => {
      let info = { power: '', cityCode: '', cityName: '' };
      if (cityOptions?.length) {
        let arr = [];
        cityOptions.forEach((item) => {
          if (item?.childDictList) {
            arr = arr.concat(item?.childDictList);
          }
        });
        if (arr.length === 1) {
          info = {
            power: 'single',
            cityCode: arr[0]?.dictValue ?? '',
            cityName: arr[0]?.dictDesc ?? '',
          };
        } else {
          // 多城市权限/没有城市权限
          info = { power: 'more', cityCode: '', cityName: '' };
        }
        cityoptionsRef.current = info;
        setCityOptions(arr);
      } else {
        message.error('当前账号未配置城市权限，请联系管理员');
      }
      setTimeout(() => {
        queryData();
      }, 0);
    };
    linkageCenter.subscribe('cityOptionsChange', cityOptionsChange);
    return () => {
      linkageCenter.unsubscribe('cityOptionsChange', cityOptionsChange);
    };
  }, []);

  useEffect(() => {
    if (aiRef.current) {
      let chartInstance = aiRef.current?.chart();
      chartInstance?.on('legendselectchanged', () => {
        aiRef.current?.resize();
      });
    }
  }, [aiRef.current]);

  return (
    <div>
      <Collapse
        onChange={(val) => {
          setActiveKey(val);
          aiRef.current?.resize();
          changeCollapse(val);
        }}
        items={items}
        defaultActiveKey={[]}
        style={{ padding: 0 }}
        activeKey={activeKey}
      />
      <LayerConfig open={open} onClose={onClose}></LayerConfig>
      <ReportDetails
        open={openDetail}
        onClose={onCloseDetail}
        cityInfo={cityInfo}
        layerType={layerType}
        baseInfo={baseInfo}
      ></ReportDetails>
      <CityDetailedData
        reportId={props.reportId}
        uuid={props.uuid}
        open={openCityDetail}
        cityInfo={cityInfo}
        cityOptions={cityOptions}
        onClose={onCloseCityDetail}
      ></CityDetailedData>
    </div>
  );
};
