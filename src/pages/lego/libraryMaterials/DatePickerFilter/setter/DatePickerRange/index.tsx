import React from 'react';
import { Select } from '@blmcp/ui';
import {
  DATE_RANGE_OPTIONS,
  DEFAULT_DATE_RANGE,
} from '../../constants/dateRangeOptions';
import styles from './index.less';
import './index.scss';

interface SetterProps {
  value: any;
  onChange: (value: any) => void;
  type?: string; // 未使用，可以标记为可选
  field?: any; // 未使用，可以标记为可选
}

export default ({ onChange, value, field }: SetterProps) => {
  const selectVal = value || DEFAULT_DATE_RANGE;

  const handleChange = (selectedValue: number) => {
    onChange(selectedValue);

    // 参考默认时间配置的逻辑，强制触发dataSetConfig变化以重置时间选择器
    const propsField = field.parent;
    const dataSetConfig = propsField.getPropValue('dataSetConfig');

    if (field.node.componentName === 'DatePickerFilter') {
      // 如果是拖拽进入的时间组件，则判断是否设置了维度，如果设置了则触发一次dataSetConfig的change
      if (dataSetConfig?.dimensionInfo?.length) {
        // 因为dataSetConfig有新旧值是否相等的判断，所以这里强制更新下
        propsField.setPropValue('dataSetConfig', {
          ...dataSetConfig,
          dimensionInfo: [
            {
              ...dataSetConfig.dimensionInfo[0],
              // 给一个随机值，目标就是为了触发dataSetConfig的变化
              // 用于重置时间选择器当跨度配置变更时
              dateRange: Math.random() + '-' + Date.now(),
            },
          ],
        });
      }
    } else if (field.node.componentName === 'DateFilterGlobal') {
      // 如果是默认的时间控件，其本身没有dataSetConfig，需要构建一个
      propsField.setPropValue('dataSetConfig', {
        dataSourceId: Math.random() + '-' + Date.now(),
      });
    }
  };

  return (
    <div className={styles['setter-date-picker-range']}>
      <div className={styles['title']}>设置筛选器可选时间跨度</div>
      <div className={styles['select-wrapper']}>
        <Select
          value={selectVal}
          options={DATE_RANGE_OPTIONS}
          onChange={handleChange}
          style={{ width: '100%' }}
          placeholder="请选择时间跨度"
        />
      </div>
    </div>
  );
};
