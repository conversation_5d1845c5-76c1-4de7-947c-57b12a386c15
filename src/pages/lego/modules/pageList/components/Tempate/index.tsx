import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
import { Skeleton } from '@blmcp/ui';
import { LoadingOutlined } from '@ant-design/icons';
import { Alert } from '@blmcp/ui';
import { throttle } from 'lodash';
import {
  blmAnalysisPageView,
  blmAnalysisModuleClick,
  sceneActionStartMonitor,
  sceneActionEndMonitor,
  blmMicrofs,
} from '@/utils/eventTracking';
import { copyTemplate, queryReportPageList } from '@/pages/lego/api';
import { ReactComponent as ReportIcon } from '@/assets/lego/report-icon.svg';
import { ReactComponent as ViewIcon } from '@/assets/lego/view.svg';
import { ReactComponent as EditIcon } from '@/assets/lego/edit.svg';
// import { ReactComponent as Img1Icon } from '@/assets/lego-template/img1.svg';
// import { ReactComponent as Img2Icon } from '@/assets/lego-template/img2.svg';
// import { ReactComponent as Img3Icon } from '@/assets/lego-template/img3.svg';
// import Img1Icon from './icon/page1.svg';
// import Img2Icon from './icon/page2.svg';
// import Img3Icon from './icon/page3.svg';
// import Img4Icon from './icon/page4.svg';
// import Img5Icon from './icon/page5.svg';
// import Img6Icon from './icon/page6.svg';
// import Img7Icon from './icon/page7.svg';
// import Img8Icon from './icon/page8.svg';
// import Img9Icon from './icon/page9.svg';
// import Img10Icon from './icon/page10.svg';
// import Img11Icon from './icon/page11.svg';
// import Img12Icon from './icon/page12.svg';

const icoList = [
  require(`./icon/page1.png`),
  require(`./icon/page2.png`),
  require(`./icon/page3.png`),
  require(`./icon/page4.png`),
  require(`./icon/page5.png`),
  require(`./icon/page6.png`),
  require(`./icon/page7.png`),
  require(`./icon/page8.png`),
  require(`./icon/page9.png`),
  require(`./icon/page10.png`),
  require(`./icon/page11.png`),
  require(`./icon/page12.png`),
];

import styles from './index.less';

const hideFilterEditPk = [
  'financeBusinessAnalysis',
  'driverStratificationResultChart',
  'driverLayeredList',
  'driverStratificationResultFK',
  'driverConversionFunnelAnalysisFK',
  'driverDailyRetentionAnalysisFK',
  'driverMonthlyRetentionAnalysisFK',
  'driverWeekRetentionAnalysisFK',
];
interface TemplateProps {
  pageListPermission: boolean;
}

const editTemplate = throttle(
  async (id, setEditId, reportName) => {
    setEditId(id);
    try {
      const res = await copyTemplate(id);
      if (res?.code === 1) {
        blmAnalysisModuleClick({
          eventId: 'e_leopard_cp_click_00003554',
          pageId: 'p_leopard_cp_00000480',
          ext: {
            str0_e: id,
            str1_e: reportName,
            str2_e: res?.data?.reportId,
            str3_e: '0',
          },
        });
        setTimeout(() => {
          window.open(
            `/qbi/legoBI/edit?reportId=${res?.data?.reportId}&overallLayoutShow=false`,
            '_blank',
          );
        });
      }
      sceneActionEndMonitor({
        sceneId: 'legoBI-copyTemplate-info',
        uniqueId: 0,
        maxTime: 6000,
      });
    } catch (error) {
      setEditId('');
    }

    setEditId('');
  },
  2000,
  { trailing: false },
);

export const Template = ({ pageListPermission }: TemplateProps) => {
  const { data, run, loading } = useRequest(queryReportPageList, {
    manual: true,
    onFinally: () => {
      // 曝光公共模板埋点
      blmMicrofs();
    },
  });

  const [editId, setEditId] = useState('');

  useEffect(() => {
    run({
      type: 3,
      pageSize: 100, // 分页阈值 必填
      pageNum: 1, // 当前页数 必填});
    });
  }, [run]);

  useEffect(() => {
    // 浏览公共模板埋点
    blmAnalysisPageView({
      pageId: 'p_leopard_cp_00000480',
      eventId: 'e_leopard_cp_pv_00002626',
    });
  }, []);

  const copyItemTemplate = (id: string, reportName: string) => () => {
    // 点击编辑按钮埋点
    blmAnalysisModuleClick({
      eventId: 'e_leopard_cp_click_00002630',
      pageId: 'p_leopard_cp_00000480',
      ext: {
        str0_e: reportName,
      },
    });
    sceneActionStartMonitor({
      sceneId: 'legoBI-copyTemplate-info',
      uniqueId: 0,
      maxTime: 6000,
    });
    editTemplate(id, setEditId, reportName);
  };

  const list = data?.data?.items ?? [];

  return (
    <div className={styles['template-container']}>
      {pageListPermission ? (
        <Alert
          style={{ marginLeft: '20px', marginTop: '5px', marginRight: '20px' }}
          message="编辑报告后，请到“我创建的“页签下查看"
          type="info"
          showIcon
          closable
        />
      ) : null}
      <div className={styles['template-wrap']}>
        {loading ? (
          <Skeleton></Skeleton>
        ) : (
          <>
            {list.map((itemList, index) => {
              return (
                <div className={styles['card']} key={itemList.id}>
                  <p
                    className={styles['name']}
                    onClick={() => {
                      // 点击查看按钮埋点
                      blmAnalysisModuleClick({
                        eventId: 'e_leopard_cp_click_00002628',
                        pageId: 'p_leopard_cp_00000480',
                        ext: {
                          str0_e: itemList.reportName,
                        },
                      });
                      blmAnalysisModuleClick({
                        eventId: 'e_leopard_cp_click_00003810',
                        pageId: 'p_leopard_cp_00000884',
                        ext: {
                          str0_e: itemList.id,
                          str1_e: 'tempate',
                        },
                      });
                      window.open(
                        `/qbi/legoBI/view?reportId=${itemList.id}&publish=${
                          itemList.status || 0
                        }`,
                        // '_self',
                      );
                    }}
                  >
                    <ReportIcon className={styles['icon']} />
                    <span className={styles['title']}>
                      {itemList.reportName}
                    </span>
                  </p>

                  <p
                    className={styles['img-wrap']}
                    onClick={() => {
                      // 点击查看按钮埋点
                      blmAnalysisModuleClick({
                        eventId: 'e_leopard_cp_click_00002628',
                        pageId: 'p_leopard_cp_00000480',
                        ext: {
                          str0_e: itemList.reportName,
                        },
                      });
                      blmAnalysisModuleClick({
                        eventId: 'e_leopard_cp_click_00003810',
                        pageId: 'p_leopard_cp_00000884',
                        ext: {
                          str0_e: itemList.id,
                          str1_e: 'tempate',
                        },
                      });
                      window.open(
                        `/qbi/legoBI/view?reportId=${itemList.id}&publish=${
                          itemList.status || 0
                        }`,
                        // '_self',
                      );
                    }}
                  >
                    <img src={icoList[index % 12]}></img>
                  </p>

                  <p className={styles['footer']}>
                    <div
                      className={styles['btn']}
                      onClick={() => {
                        // 点击查看按钮埋点
                        blmAnalysisModuleClick({
                          eventId: 'e_leopard_cp_click_00002628',
                          pageId: 'p_leopard_cp_00000480',
                          ext: {
                            str0_e: itemList.reportName,
                          },
                        });
                        blmAnalysisModuleClick({
                          eventId: 'e_leopard_cp_click_00003810',
                          pageId: 'p_leopard_cp_00000884',
                          ext: {
                            str0_e: itemList.id,
                            str1_e: 'tempate',
                          },
                        });
                        window.open(
                          `/qbi/legoBI/view?reportId=${itemList.id}&publish=${
                            itemList.status || 0
                          }`,
                          // '_self',
                        );
                      }}
                    >
                      <ViewIcon className={styles['operation-icon']} />
                      查看
                    </div>

                    {pageListPermission &&
                    !hideFilterEditPk.some(
                      (v) => itemList.pageMark?.includes(v),
                    ) ? (
                      <div
                        className={styles['btn']}
                        style={{ borderLeft: '1px solid #E7E8EB' }}
                        onClick={copyItemTemplate(
                          itemList.id,
                          itemList.reportName,
                        )}
                      >
                        {editId === itemList.id ? (
                          <LoadingOutlined
                            spin
                            className={styles['operation-icon']}
                          />
                        ) : (
                          <EditIcon className={styles['operation-icon']} />
                        )}
                        编辑
                      </div>
                    ) : null}
                  </p>
                </div>
              );
            })}
            <i></i>
            <i></i>
            <i></i>
            <i></i>
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </>
        )}
      </div>
    </div>
  );
};
