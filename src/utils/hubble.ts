import { TrademarkOutlined } from '@ant-design/icons';
import { isInIcestark } from '@ice/stark-app';

// 是否为哈勃场景
// export const isHubble = !!(
//   isInIcestark() || window.top?.['ICESTARK']?.['root']
// );
export const isHubble = true;

// 替换接口
export const HubbleApi: { [key: string]: string } = {
  '/bos/admin/v1/common/dict/getBatch':
    '/lego/admin/v1/ai/chart-manager/bos/v1/common/dict/getBatch',
  '/admin/v1/manage/safety/textListScan':
    '/dockingApi/v1/admin/invoker/antispamService_textListScanV2',
};
